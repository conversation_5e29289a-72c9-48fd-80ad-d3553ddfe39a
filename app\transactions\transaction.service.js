﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const {
  calculateTransactionAmounts,
} = require("../_helpers/donationConstants");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  getDonationSummary,
  getTransactionDetails,
};

db.Transaction.belongsTo(db.User, {
  as: "userInfo",
  through: "users",
  foreignKey: "user_id",
  otherKey: "user_id",
});

db.Transaction.belongsTo(db.Order, {
  as: "orderInfo",
  through: "orders",
  foreignKey: "order_id",
  otherKey: "order_id",
});

// db.Transaction.belongsTo(db.Ngo, {
//   as: "ngoInfo",
//   through: "ngos",
//   foreignKey: "ngo_id",
//   otherKey: "ngo_id",
// });
// db.Transaction.belongsTo(db.Campaign, {
//   as: "campaignInfo",
//   through: "campaigns",
//   foreignKey: "campaign_id",
//   otherKey: "campaign_id",
// });

async function getAll(params, page = 1, limit = 10) {
  const { ngoId, userId, paymentId } = params;
  const where = {};
  if (ngoId) {
    where.ngo_id = ngoId;
  }
  if (paymentId) {
    where.razorpay_payment_id = paymentId;
  }
  if (userId) {
    where.user_id = userId;
  }
  const offset = (parseInt(page) - 1) * parseInt(limit);

  const { count, rows } = await db.Transaction.findAndCountAll({
    order: [["id", "DESC"]],
    limit: parseInt(limit),
    offset: offset,

    where: where,
    include: [
      {
        model: db.User,
        as: "userInfo",
      },
      {
        model: db.Order,
        as: "orderInfo",
      },
    ],
  });

  return {
    transactions: rows,
    totalCount: count,
    totalPages: Math.ceil(count / limit),
    currentPage: page,
  };
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  // If transaction calculations are needed and not already provided
  // (for standalone transaction creation with base_amount, dr_tip, user_convenience, total_amount)
  if (
    params.base_amount &&
    params.dr_tip &&
    params.user_convenience &&
    params.amount &&
    !params.payment_gateway_charges
  ) {
    const transactionCalculations = calculateTransactionAmounts(
      params.amount,
      params.base_amount,
      params.dr_tip,
      params.user_convenience
    );

    // Add calculated fields to params
    Object.assign(params, {
      payment_gateway_charges: transactionCalculations.paymentGatewayCharges,
      dr_tip_after_charges: transactionCalculations.drTipAfterCharges,
      user_convenience_after_charges:
        transactionCalculations.userConvenienceAfterCharges,
      ngo_platform_convenience_fee:
        transactionCalculations.ngoPlatformConvenienceFee,
      ngo_net_donation: transactionCalculations.ngoNetDonation,
    });
  }

  const record = await db.Transaction.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // copy params to Transaction and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.Transaction.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}

async function getDonationSummary({ donationType, userId }) {
  const where = {};
  if (userId) where.user_id = userId;
  if (donationType && donationType !== "all")
    where.donation_type = donationType;

  const transactions = await db.Transaction.findAll({ where });

  let totalAmount = 0;
  const donations = [];

  for (const tx of transactions) {
    totalAmount += parseFloat(tx.amount);
    const createdAt = tx.createdAt;
    const orderId = tx.order_id;

    if (tx.donation_type === "ngo") {
      const ngo = await db.Ngo.findByPk(tx.ngo_id);
      if (ngo) {
        donations.push({
          type: "ngo",
          id: ngo.id,
          name: ngo.name,
          amount: parseFloat(tx.amount),
          createdAt,
          orderId,
          transactionId: tx.id,
        });
      }
    } else if (tx.donation_type === "campaign") {
      const campaign = await db.Campaign.findByPk(tx.campaign_id);
      let ngoName = null;
      if (campaign) {
        const ngo = await db.Ngo.findByPk(campaign.ngo_id);
        if (ngo) ngoName = ngo.name;

        donations.push({
          type: "campaign",
          id: campaign.id,
          name: campaign.name,
          ngoName,
          amount: parseFloat(tx.amount),
          createdAt,
          orderId,
          transactionId: tx.id,
        });
      }
    } else if (tx.donation_type === "genericViaMoney") {
      const bucket = await db.Bucket.findByPk(tx.bucket_id);
      const bucketItems = await db.BucketItem.findAll({
        where: { bucket_id: tx.bucket_id },
      });

      const uniqueNgoIds = new Set();
      let campaignCount = 0;

      for (const item of bucketItems) {
        if (item.ngo_id) uniqueNgoIds.add(item.ngo_id);
        if (item.campaign_id) campaignCount++;
      }

      if (bucket) {
        donations.push({
          type: "generic",
          name: bucket.name,
          ngoCount: uniqueNgoIds.size,
          campaignCount,
          amount: parseFloat(tx.amount),
          createdAt,
          orderId,
          transactionId: tx.id,
        });
      }
    }
  }

  return {
    totalAmount,
    donations,
  };
}

async function getTransactionDetails(params) {
  try {
    const { transactionId } = params;

    const transaction = await db.Transaction.findByPk(transactionId);
    const orderData = await db.Order.findByPk(transaction?.order_id);
    if (!transaction) throw new Error("Transaction not found");

    const baseData = {
      id: transaction.id,
      amount: parseFloat(transaction.amount),
      processingFee: 100,
      createdAt: transaction?.createdAt,
      orderId: transaction?.order_id,
      transactionInfo: transaction,
      purpose: orderData?.purpose || null,
      purpose_donationday: orderData?.purpose_donationday || null,
      remarks: orderData?.remarks || null,
    };

    if (transaction.donation_type === "ngo") {
      const ngo = await db.Ngo.findByPk(transaction.ngo_id);
      if (!ngo) throw new Error("NGO not found");

      const ngoCategory = await db.NgoCategory.findOne({
        where: { ngo_id: ngo.id },
      });
      const category = ngoCategory
        ? await db.Category.findByPk(ngoCategory.category_id)
        : null;

      return {
        ...baseData,
        type: "ngo",
        ngo: {
          id: ngo.id,
          name: ngo.name,
          description: ngo.description,
          fileName: ngo.fileName,
          grade: ngo.grade,
          category: category?.name || null,
        },
      };
    }

    if (transaction.donation_type === "campaign") {
      const campaign = await db.Campaign.findByPk(transaction.campaign_id);
      if (!campaign) throw new Error("Campaign not found");

      return {
        ...baseData,
        type: "campaign",
        campaign: {
          id: campaign.id,
          name: campaign.name,
          description: campaign.description,
          fileName: campaign.fileName,
        },
      };
    }

    if (transaction.donation_type === "genericViaMoney") {
      const bucket = await db.Bucket.findByPk(transaction.bucket_id);
      if (!bucket) throw new Error("Bucket not found");

      const bucketItems = await db.BucketItem.findAll({
        where: { bucket_id: bucket.id },
      });

      const ngoMap = new Map();
      const campaignMap = new Map();

      for (const item of bucketItems) {
        if (item.ngo_id && !ngoMap.has(item.ngo_id)) {
          const ngo = await db.Ngo.findByPk(item.ngo_id);
          if (ngo) {
            const ngoCategory = await db.NgoCategory.findOne({
              where: { ngo_id: ngo.id },
            });
            const category = ngoCategory
              ? await db.Category.findByPk(ngoCategory.category_id)
              : null;

            ngoMap.set(ngo.id, {
              id: ngo.id,
              name: ngo.name,
              description: ngo.description,
              fileName: ngo.fileName,
              grade: ngo.grade,
              category: category?.name || null,
            });
          }
        }

        if (item.campaign_id && !campaignMap.has(item.campaign_id)) {
          const campaign = await db.Campaign.findByPk(item.campaign_id);
          if (campaign) {
            campaignMap.set(campaign.id, {
              id: campaign.id,
              name: campaign.name,
              description: campaign.description,
              fileName: campaign.fileName,
            });
          }
        }
      }

      return {
        ...baseData,
        type: "generic",
        bucket: {
          id: bucket.id,
          name: bucket.name,
          ngos: Array.from(ngoMap.values()),
          campaigns: Array.from(campaignMap.values()),
        },
      };
    }

    throw new Error("Invalid transaction type");
  } catch (err) {
    throw err; // Let the controller or caller handle the error response
  }
}
