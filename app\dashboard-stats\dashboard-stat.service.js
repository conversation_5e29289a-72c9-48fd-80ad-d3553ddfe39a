const { Op } = require("sequelize");
const db = require("../_helpers/db");
const { format, subDays, differenceInDays } = require("date-fns");
const documentMasterService = require("../document-master/document-master.service");
const documentuserService = require("../document-users/document-user.service");

module.exports = {
  create,
  getOperationsData,
  addRecord,
};

async function create(params) {
  const record = await db.DashboardStat.create(params);
  return record;
}

async function addRecord(type, typeid, dateRecorded = null) {
  const recordDate = dateRecorded || format(new Date(), "yyyy-MM-dd");

  // Check if record already exists for same date and typeid combination
  const existingRecord = await db.DashboardStat.findOne({
    where: {
      type: type,
      typeid: typeid,
      dateRecorded: recordDate,
    },
  });

  // If record exists, ignore it
  if (existingRecord) {
    return {
      message: "Record already exists for this date and type combination",
      existing: true,
    };
  }

  // Create new record
  const newRecord = await db.DashboardStat.create({
    type: type,
    typeid: typeid,
    dateRecorded: recordDate,
  });

  return { record: newRecord, existing: false };
}

async function getOperationsData() {
  const today = format(new Date(), "yyyy-MM-dd");

  // 1a. Daily Active Users
  const dailyActiveUsers = await getDailyActiveUsers(today);

  // 1b. Donation Analytics
  const donationAnalytics = await getDonationAnalytics(today);

  // 1c. Volunteer Count
  const volunteerCount = await getVolunteerCount(today);

  // 1e. Campaign Click-Through Rate
  const campaignCTR = await getCampaignCTR(today);

  // 1f. Today's Campaign Creation Count
  const todaysCampaignCount = await getTodaysCampaignCount(today);

  // 1g. Today's Total Donations
  const todaysTotalDonations = await getTodaysTotalDonations(today);

  // New additions
  // NGOs onboarded today
  const ngosOnboarded = await getNgosOnboarded(today);

  // Today's campaigns with details and CTR
  const todaysCampaigns = await getTodaysCampaigns(today);

  // NGOs assigned in last 5 days with document tracking
  const ngosAssignedLast5Days = await getNgosAssignedLast5Days(today);

  return {
    date: today,
    dailyActiveUsers,
    donationAnalytics,
    volunteerCount,
    campaignCTR,
    todaysCampaignCount,
    todaysTotalDonations,
    ngosOnboarded,
    todaysCampaigns,
    ngosAssignedLast5Days,
  };
}

async function getDailyActiveUsers(date) {
  const count = await db.DashboardStat.count({
    where: {
      type: "user",
      dateRecorded: date,
    },
    distinct: true,
    col: "typeid",
  });

  return {
    count,
    description: "Unique users active today",
  };
}

async function getDonationAnalytics(date) {
  const donations = await db.Order.findAll({
    where: {
      createdAt: {
        [Op.gte]: new Date(date + " 00:00:00"),
        [Op.lte]: new Date(date + " 23:59:59"),
      },
      order_status: {
        [Op.eq]: "Confirmed",
      },
      payment_status: {
        [Op.eq]: "Confirmed",
      },
    },
    attributes: ["user_id", "total_price"],
  });

  const totalDonationAmount = donations.reduce((sum, order) => {
    return sum + parseFloat(order.total_price || 0);
  }, 0);

  const uniqueUserIds = [...new Set(donations.map((order) => order.user_id))];
  console.log("uniqueUserIds", uniqueUserIds);
  const uniqueUserCount = uniqueUserIds.length;

  const averageDonationPerUser =
    uniqueUserCount > 0 ? totalDonationAmount / uniqueUserCount : 0;

  return {
    totalDonationAmount: parseFloat(totalDonationAmount.toFixed(2)),
    uniqueUserCount,
    averageDonationPerUser: parseFloat(averageDonationPerUser.toFixed(2)),
    description: "Today's donation analytics",
  };
}

async function getVolunteerCount(date) {
  const volunteerUsers = await db.User.count({
    where: {
      createdAt: {
        [Op.gte]: new Date(date + " 00:00:00"),
        [Op.lte]: new Date(date + " 23:59:59"),
      },
      purposes: {
        [Op.like]: "%volunteer%",
      },
    },
  });

  return {
    count: volunteerUsers,
    description: "Total users with volunteer purpose",
  };
}

async function getCampaignCTR(date) {
  const campaignViews = await db.DashboardStat.count({
    where: {
      type: "campaign",
      dateRecorded: date,
    },
  });

  const totalCampaigns = await db.Campaign.count();

  const ctr = totalCampaigns > 0 ? (campaignViews / totalCampaigns) * 100 : 0;

  return {
    campaignViews,
    ctr: parseFloat(ctr.toFixed(2)),
    description: "Campaign Click-Through Rate for today",
  };
}

async function getTodaysCampaignCount(date) {
  const count = await db.DashboardStat.count({
    where: {
      type: "campaign",
      dateRecorded: date,
    },
  });

  return {
    count,
    description: "Campaigns created today",
  };
}

async function getTodaysTotalDonations(date) {
  const result = await db.Order.findOne({
    where: {
      createdAt: {
        [Op.gte]: new Date(date + " 00:00:00"),
        [Op.lte]: new Date(date + " 23:59:59"),
      },
      order_status: {
        [Op.eq]: "Confirmed",
      },
      payment_status: {
        [Op.eq]: "Confirmed",
      },
    },
    attributes: [
      [db.sequelize.fn("SUM", db.sequelize.col("total_price")), "totalAmount"],
      [db.sequelize.fn("COUNT", db.sequelize.col("id")), "totalOrders"],
    ],
  });

  return {
    totalAmount: parseFloat(result?.dataValues?.totalAmount || 0),
    totalOrders: parseInt(result?.dataValues?.totalOrders || 0),
    description: "Total donations made today",
  };
}

async function getNgosOnboarded(date) {
  const count = await db.Ngo.count({
    where: {
      assigned_on: {
        [Op.gte]: new Date(date + " 00:00:00"),
        [Op.lte]: new Date(date + " 23:59:59"),
      },
    },
  });

  return {
    count,
    description: "NGOs onboarded today (assigned_on = today)",
  };
}

async function getTodaysCampaigns(date) {
  // Get campaigns created today
  const campaigns = await db.Campaign.findAll({
    where: {
      createdAt: {
        [Op.gte]: new Date(date + " 00:00:00"),
        [Op.lte]: new Date(date + " 23:59:59"),
      },
    },
    attributes: ["id", "name", "status", "createdAt", "ngo_id"],
    order: [["createdAt", "DESC"]],
  });

  // Calculate CTR for each campaign
  const campaignsWithCTR = await Promise.all(
    campaigns.map(async (campaign) => {
      const campaignViews = await db.DashboardStat.count({
        where: {
          type: "campaign",
          typeid: campaign.id,
          dateRecorded: date,
        },
      });

      let ngo;
      if (campaign.ngo_id) {
        ngo = await db.Ngo.findOne({
          where: { id: campaign.ngo_id },
          attributes: ["name"],
        });
      }

      // For individual campaign CTR, we can use views as a metric
      // CTR could be calculated as views per campaign or other metrics
      const ctr = campaignViews; // Simple view count as CTR metric

      return {
        id: campaign.id,
        name: campaign.name,
        status: campaign.status,
        createdAt: campaign.createdAt,
        ctr: ctr,
        ngo_name: ngo?.name || null,
        views: campaignViews,
      };
    })
  );

  return {
    campaigns: campaignsWithCTR,
    totalCount: campaigns.length,
    description: "Campaigns created today with CTR metrics",
  };
}

async function getNgosAssignedLast5Days(date) {
  const fiveDaysAgo = new Date(date);
  fiveDaysAgo.setDate(fiveDaysAgo.getDate() - 5);

  // Get NGOs assigned in last 5 days
  const ngos = await db.Ngo.findAll({
    where: {
      assigned_on: {
        [Op.gte]: fiveDaysAgo,
        [Op.lte]: new Date(date + " 23:59:59"),
      },
    },
    attributes: [
      "id",
      "name",
      "current_address",
      "ngo_status",
      "assigned_on",
      "ngo_type",
    ],
    order: [["assigned_on", "DESC"]],
  });

  // Process each NGO to get category and document information
  const ngosWithDetails = await Promise.all(
    ngos.map(async (record) => {
      // Get category information
      const ngoCategories = await db.NgoCategory.findAll({
        where: { ngo_id: record.id },
        include: [
          {
            model: db.Category,
            as: "categoryInfo",
            attributes: ["id", "name"],
          },
        ],
      });

      const categoryNames = ngoCategories.map(
        (cat) => cat.categoryInfo?.name || "Unknown"
      );

      // Get document information using the same logic as provided
      const [documents, docsSubmitted] = await Promise.all([
        documentMasterService.getAllForNgoSearch({
          type: "NGO",
          documentType: record.ngo_type,
        }),
        documentuserService.getAll({ ngo_id: record.id }),
      ]);

      // Extract document data
      const extractedDocumentData = documents.map(
        ({ dataValues }) => dataValues
      );

      // Extract submitted document data
      const extractedSubmittedDocumentData = docsSubmitted.map(
        ({ dataValues }) => dataValues
      );
      const submittedDocIds = extractedSubmittedDocumentData.map(
        (doc) => doc.documentId
      );

      // Calculate days passed since assigned_on
      const daysPassed = differenceInDays(
        new Date(date),
        new Date(record.assigned_on)
      );

      return {
        id: record.id,
        name: record.name,
        current_address: record.current_address,
        ngo_status: record.ngo_status,
        assigned_on: record.assigned_on,
        daysPassed: daysPassed,
        categories: categoryNames,
        uploadedDocs: submittedDocIds.length,
        totalMandatoryDocs: extractedDocumentData.length,
        documentCompletionPercentage:
          extractedDocumentData.length > 0
            ? Math.round(
                (submittedDocIds.length / extractedDocumentData.length) * 100
              )
            : 0,
      };
    })
  );

  return {
    ngos: ngosWithDetails,
    totalCount: ngos.length,
    description: "NGOs assigned in last 5 days with document tracking",
  };
}
