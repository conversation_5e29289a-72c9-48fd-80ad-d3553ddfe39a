const { Op } = require("sequelize");
const db = require("../_helpers/db");
const { format } = require("date-fns");

module.exports = {
  create,
  getOperationsData,
  addRecord,
};

async function create(params) {
  const record = await db.DashboardStat.create(params);
  return record;
}

async function addRecord(type, typeid, dateRecorded = null) {
  const recordDate = dateRecorded || format(new Date(), "yyyy-MM-dd");

  // Check if record already exists for same date and typeid combination
  const existingRecord = await db.DashboardStat.findOne({
    where: {
      type: type,
      typeid: typeid,
      dateRecorded: recordDate,
    },
  });

  // If record exists, ignore it
  if (existingRecord) {
    return {
      message: "Record already exists for this date and type combination",
      existing: true,
    };
  }

  // Create new record
  const newRecord = await db.DashboardStat.create({
    type: type,
    typeid: typeid,
    dateRecorded: recordDate,
  });

  return { record: newRecord, existing: false };
}

async function getOperationsData() {
  const today = format(new Date(), "yyyy-MM-dd");

  // 1a. Daily Active Users
  const dailyActiveUsers = await getDailyActiveUsers(today);

  // 1b. Donation Analytics
  const donationAnalytics = await getDonationAnalytics(today);

  // 1c. Volunteer Count
  const volunteerCount = await getVolunteerCount(today);

  // 1e. Campaign Click-Through Rate
  const campaignCTR = await getCampaignCTR(today);

  // 1f. Today's Campaign Creation Count
  const todaysCampaignCount = await getTodaysCampaignCount(today);

  // 1g. Today's Total Donations
  const todaysTotalDonations = await getTodaysTotalDonations(today);

  return {
    date: today,
    dailyActiveUsers,
    donationAnalytics,
    volunteerCount,
    campaignCTR,
    todaysCampaignCount,
    todaysTotalDonations,
  };
}

async function getDailyActiveUsers(date) {
  const count = await db.DashboardStat.count({
    where: {
      type: "user",
      dateRecorded: date,
    },
    distinct: true,
    col: "typeid",
  });

  return {
    count,
    description: "Unique users active today",
  };
}

async function getDonationAnalytics(date) {
  const donations = await db.Order.findAll({
    where: {
      createdAt: {
        [Op.gte]: new Date(date + " 00:00:00"),
        [Op.lte]: new Date(date + " 23:59:59"),
      },
      order_status: {
        [Op.eq]: "Confirmed",
      },
      payment_status: {
        [Op.eq]: "Confirmed",
      },
    },
    attributes: ["user_id", "total_price"],
  });

  const totalDonationAmount = donations.reduce((sum, order) => {
    return sum + parseFloat(order.total_price || 0);
  }, 0);

  const uniqueUserIds = [...new Set(donations.map((order) => order.user_id))];
  console.log("uniqueUserIds", uniqueUserIds);
  const uniqueUserCount = uniqueUserIds.length;

  const averageDonationPerUser =
    uniqueUserCount > 0 ? totalDonationAmount / uniqueUserCount : 0;

  return {
    totalDonationAmount: parseFloat(totalDonationAmount.toFixed(2)),
    uniqueUserCount,
    averageDonationPerUser: parseFloat(averageDonationPerUser.toFixed(2)),
    description: "Today's donation analytics",
  };
}

async function getVolunteerCount(date) {
  const volunteerUsers = await db.User.count({
    where: {
      createdAt: {
        [Op.gte]: new Date(date + " 00:00:00"),
        [Op.lte]: new Date(date + " 23:59:59"),
      },
      purposes: {
        [Op.like]: "%volunteer%",
      },
    },
  });

  return {
    count: volunteerUsers,
    description: "Total users with volunteer purpose",
  };
}

async function getCampaignCTR(date) {
  const campaignViews = await db.DashboardStat.count({
    where: {
      type: "campaign",
      dateRecorded: date,
    },
  });

  const totalCampaigns = await db.Campaign.count();

  const ctr = totalCampaigns > 0 ? (campaignViews / totalCampaigns) * 100 : 0;

  return {
    campaignViews,
    ctr: parseFloat(ctr.toFixed(2)),
    description: "Campaign Click-Through Rate for today",
  };
}

async function getTodaysCampaignCount(date) {
  const count = await db.DashboardStat.count({
    where: {
      type: "campaign",
      dateRecorded: date,
    },
  });

  return {
    count,
    description: "Campaigns created today",
  };
}

async function getTodaysTotalDonations(date) {
  const result = await db.Order.findOne({
    where: {
      createdAt: {
        [Op.gte]: new Date(date + " 00:00:00"),
        [Op.lte]: new Date(date + " 23:59:59"),
      },
      order_status: {
        [Op.eq]: "Confirmed",
      },
      payment_status: {
        [Op.eq]: "Confirmed",
      },
    },
    attributes: [
      [db.sequelize.fn("SUM", db.sequelize.col("total_price")), "totalAmount"],
      [db.sequelize.fn("COUNT", db.sequelize.col("id")), "totalOrders"],
    ],
  });

  return {
    totalAmount: parseFloat(result?.dataValues?.totalAmount || 0),
    totalOrders: parseInt(result?.dataValues?.totalOrders || 0),
    description: "Total donations made today",
  };
}
