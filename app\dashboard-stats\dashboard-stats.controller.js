const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const dashboardStatsService = require("./dashboard-stat.service");
const { logAction } = require("../_helpers/logger");

// routes
router.post("/", addRecordSchema, addRecord);
router.get("/operations-data", getOperationsData);

module.exports = router;

function addRecordSchema(req, res, next) {
  const schema = Joi.object({
    type: Joi.string().required(),
    typeid: Joi.number().integer().required(),
    dateRecorded: Joi.string().optional(),
  });
  validateRequest(req, next, schema);
}

function addRecord(req, res, next) {
  const { type, typeid, dateRecorded } = req.body;
  
  dashboardStatsService
    .addRecord(type, typeid, dateRecorded)
    .then((result) => {
      if (result.existing) {
        res.json({
          status: true,
          message: result.message,
          data: null,
        });
      } else {
        logAction(
          req,
          `Added dashboard stats record: type=${type}, typeid=${typeid}`,
          "CREATE"
        );
        res.json({
          status: true,
          message: "Dashboard stats record created successfully",
          data: result.record,
        });
      }
    })
    .catch(next);
}

function getOperationsData(req, res, next) {
  dashboardStatsService
    .getOperationsData()
    .then((data) => {
      logAction(req, "Fetched operations dashboard data", "READ");
      res.json({
        status: true,
        message: "Operations data retrieved successfully",
        data: data,
      });
    })
    .catch(next);
}
