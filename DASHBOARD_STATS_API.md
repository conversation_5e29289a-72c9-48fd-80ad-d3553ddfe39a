# Dashboard Stats API Documentation

## Overview

The Dashboard Stats API provides comprehensive analytics and tracking functionality for the doRight platform. It tracks user activities, campaign interactions, donations, and provides real-time operational data for dashboard insights.

## Base URL
```
/api/dashboard-stats
```

## Endpoints

### 1. Add Dashboard Stats Record

**Endpoint:** `POST /api/dashboard-stats`

**Description:** Adds a new record to the dashboard stats table. If a record already exists for the same date and type combination, it will be ignored.

**Request Body:**
```json
{
  "type": "string",        // Required: Type of record (e.g., "user", "campaign", "campaign_created")
  "typeid": "number",      // Required: ID of the entity being tracked
  "dateRecorded": "string" // Optional: Date in YYYY-MM-DD format (defaults to today)
}
```

**Response:**
```json
{
  "status": true,
  "message": "Dashboard stats record created successfully",
  "data": {
    "id": 1,
    "type": "user",
    "typeid": 123,
    "dateRecorded": "2024-01-15",
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

**Example Usage:**
```bash
curl -X POST http://localhost:3000/api/dashboard-stats \
  -H "Content-Type: application/json" \
  -d '{
    "type": "user",
    "typeid": 123
  }'
```

### 2. Get Operations Data

**Endpoint:** `GET /api/dashboard-stats/operations-data`

**Description:** Retrieves comprehensive operational analytics for today's date including user activity, donations, campaigns, and volunteer metrics.

**Response:**
```json
{
  "status": true,
  "message": "Operations data retrieved successfully",
  "data": {
    "date": "2024-01-15",
    "dailyActiveUsers": {
      "count": 45,
      "description": "Unique users active today"
    },
    "donationAnalytics": {
      "totalDonationAmount": 15750.50,
      "uniqueUserCount": 23,
      "averageDonationPerUser": 684.37,
      "description": "Today's donation analytics"
    },
    "volunteerCount": {
      "count": 12,
      "description": "Total users with volunteer purpose"
    },
    "campaignCTR": {
      "campaignViews": 89,
      "totalLiveCampaigns": 15,
      "ctr": 593.33,
      "description": "Campaign Click-Through Rate for today"
    },
    "todaysCampaignCount": {
      "count": 3,
      "description": "Campaigns created today"
    },
    "todaysTotalDonations": {
      "totalAmount": 15750.50,
      "totalOrders": 28,
      "description": "Total donations made today"
    }
  }
}
```

**Example Usage:**
```bash
curl -X GET http://localhost:3000/api/dashboard-stats/operations-data
```

## Data Types and Tracking

### Supported Record Types

1. **"user"** - Tracks daily active users
   - `typeid`: User ID
   - Used for: Daily active user count

2. **"campaign"** - Tracks campaign views/clicks
   - `typeid`: Campaign ID
   - Used for: Click-through rate calculation

3. **"campaign_created"** - Tracks campaign creation
   - `typeid`: Campaign ID
   - Used for: Daily campaign creation count

4. **"campaign_updated"** - Tracks campaign updates
   - `typeid`: Campaign ID
   - Used for: Campaign modification tracking

## Automatic Tracking

The system automatically tracks certain events:

### Campaign Views
- **Trigger:** When `GET /api/campaigns/:id` is called
- **Record:** `type: "campaign", typeid: campaignId, dateRecorded: today`
- **Purpose:** Calculate click-through rates

### Campaign Creation
- **Trigger:** When `POST /api/campaigns` is called
- **Record:** `type: "campaign_created", typeid: campaignId, dateRecorded: today`
- **Purpose:** Track daily campaign creation

### Campaign Updates
- **Trigger:** When `PUT /api/campaigns/:id` is called
- **Record:** `type: "campaign_updated", typeid: campaignId, dateRecorded: today`
- **Purpose:** Track campaign modifications

## Analytics Calculations

### Daily Active Users
- Counts unique `typeid` values where `type = "user"` for today's date

### Donation Analytics
- **Total Donation Amount:** Sum of `total_price` from orders created today (excluding cancelled orders)
- **Unique User Count:** Count of distinct `user_id` from today's orders
- **Average Donation Per User:** Total donation amount ÷ unique user count

### Volunteer Count
- Counts users where `purposes` field contains the word "volunteer" (case-insensitive)

### Campaign Click-Through Rate (CTR)
- **Campaign Views:** Count of records where `type = "campaign"` for today
- **Total Live Campaigns:** Count of campaigns with `status = "Live"`
- **CTR:** (Campaign Views ÷ Total Live Campaigns) × 100

### Today's Campaign Count
- Counts records where `type = "campaign_created"` for today's date

### Today's Total Donations
- **Total Amount:** Sum of `total_price` from orders created today
- **Total Orders:** Count of orders created today (excluding cancelled)

## Error Handling

The API includes comprehensive error handling:

- **Validation Errors:** Returns 400 with validation details
- **Duplicate Records:** Silently ignores with success message
- **Database Errors:** Returns 500 with error details
- **Tracking Errors:** Logged but don't affect main operations

## Usage Examples

### Track User Activity
```javascript
// Track when a user performs an action
fetch('/api/dashboard-stats', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    type: 'user',
    typeid: userId
  })
});
```

### Get Dashboard Data
```javascript
// Fetch today's operational metrics
fetch('/api/dashboard-stats/operations-data')
  .then(response => response.json())
  .then(data => {
    console.log('Daily Active Users:', data.data.dailyActiveUsers.count);
    console.log('Total Donations:', data.data.todaysTotalDonations.totalAmount);
    console.log('Campaign CTR:', data.data.campaignCTR.ctr + '%');
  });
```

## Database Schema

### dashboard_stats Table
```sql
CREATE TABLE dashboard_stats (
  id INT PRIMARY KEY AUTO_INCREMENT,
  type VARCHAR(255) NOT NULL,
  typeid INT NOT NULL,
  dateRecorded DATE NOT NULL,
  createdBy INT NULL,
  updatedBy INT NULL,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Indexes (Recommended)
```sql
CREATE INDEX idx_dashboard_stats_type_date ON dashboard_stats(type, dateRecorded);
CREATE INDEX idx_dashboard_stats_typeid_date ON dashboard_stats(typeid, dateRecorded);
```

## Notes

- All date calculations are based on the server's local timezone
- Duplicate prevention is based on `type`, `typeid`, and `dateRecorded` combination
- Campaign tracking is automatically handled by the system
- Manual user activity tracking requires explicit API calls
- All monetary values are returned as floating-point numbers with 2 decimal places
