const config = require("../../config.json");
const mysql = require("mysql2/promise");
const { Sequelize } = require("sequelize");

module.exports = db = {};

initialize();
async function initialize() {
  // create db if it doesn't already exist
  const environment = config.environment;
  let databaseValue = null;

  if (environment === "UAT") {
    databaseValue = config.fatDatabase;
  } else if (environment === "PROD") {
    databaseValue = config.prodDatabase;
  } else {
    databaseValue = config.database;
  }
  const { host, port, user, password, database } = databaseValue;
  // connect to dbs
  const sequelize = new Sequelize(database, user, password, {
    host: host,
    port: port,
    dialect: "mysql",
    pool: {
      max: 100,
      min: 0,
      idle: 5000,
      evict: 10000,
    },
  });

  sequelize
    .authenticate()
    .then(() => {
      console.log("Connection has been established successfully.");
    })
    .catch((error) => {
      console.error("Unable to connect to the database: ", error);
    });

  // init models and add them to the exported db object

  db.Role = require("../roles/role.model")(sequelize);
  db.Ngo = require("../ngos/ngo.model")(sequelize);
  db.Newsletter = require("../newsletters/newsletter.model")(sequelize);
  db.CommunicationEmail =
    require("../communication-email/communication-email.model")(sequelize);

  db.SettingsMaster = require("../ngo-setting-master/ngo-setting-master.model")(
    sequelize
  );
  db.NgoSetting = require("../ngo-settings/ngo-setting.model")(sequelize);
  db.Data = require("../data_insert/data.model")(sequelize);
  db.PortalUser = require("../portalusers/portaluser.model")(sequelize);
  db.User = require("../users/user.model")(sequelize);
  db.State = require("../states/state.model")(sequelize);
  db.Document = require("../document-master/document-master.model")(sequelize);
  db.Campaign = require("../campaigns/campaign.model")(sequelize);

  db.Category = require("../categories/category.model")(sequelize);
  db.RSVPInfo = require("../rsvp-informations/rsvp-information.model")(
    sequelize
  );
  db.Order = require("../orders/order.model")(sequelize);
  db.SubCategory = require("../sub-categories/sub-category.model")(sequelize);
  db.Skill = require("../skills/skill.model")(sequelize);
  db.Slide = require("../slides/slide.model")(sequelize);
  db.Product = require("../products/product.model")(sequelize);
  db.Question = require("../questions/question.model")(sequelize);
  db.Collection = require("../collections/collection.model")(sequelize);

  db.Auditlog = require("../auditlogs/auditlog.model")(sequelize);
  db.NotificationNgo = require("../notification-ngos/notification-ngo.model")(
    sequelize
  );

  db.CampaignCategoryLocations =
    require("../campaign-category-locations/campaign-category-location.model")(
      sequelize
    );
  db.CampaignRsvps = require("../campaign_rsvps/campaign_rsvp.model")(
    sequelize
  );
  db.NgoCategory = require("../ngo-categories/ngo_category.model")(sequelize);
  db.DocumentUser = require("../document-users/document-user.model")(sequelize);
  db.UserSkill = require("../user-skills/user-skill.model")(sequelize);
  db.DocumentUser = require("../document-users/document-user.model")(sequelize);
  db.Transaction = require("../transactions/transaction.model")(sequelize);
  db.Theme = require("../themes/theme.model")(sequelize);
  db.OrderDonation = require("../order-donations/order-donation.model")(
    sequelize
  );
  db.ThemeBannerImage =
    require("../themes-banner-images/themes-banner-image.model")(sequelize);
  db.ThemeImpactArea = require("../theme-impactareas/theme-impactarea.model")(
    sequelize
  );
  db.CampaignMilestone =
    require("../campaign-milestones/campaign-milestone.model")(sequelize);
  db.Item = require("../items/item.model")(sequelize);
  db.KindDonation =
    require("../campaign-kind-donation/campaign-kind-donation.model")(
      sequelize
    );
  db.NgoType = require("../ngo-types/ngo-type.model")(sequelize);
  db.Journal = require("../journals/journal.model")(sequelize);
  db.OrderCampaign = require("../order-campaigns/order-campaign.model")(
    sequelize
  );
  db.OrderNgo = require("../order-ngos/order-ngo.model")(sequelize);
  db.Faqs = require("../faqs/faq.model")(sequelize);
  db.NgoImage = require("../ngo-images/ngo-image.model")(sequelize);
  db.ProductImage = require("../product-images/product-image.model")(sequelize);
  db.CategoryImage = require("../category-images/category-image.model")(
    sequelize
  );
  db.Review = require("../reviews/review.model")(sequelize);
  db.UserToken = require("../user-tokens/user-token.model")(sequelize);
  db.CampaignImage = require("../campaigns-images/campaign-image.model")(
    sequelize
  );
  db.BankDetail = require("../bank-details/bank-detail.model")(sequelize);
  db.TempUser = require("../temp-users/temp-user.model")(sequelize);
  db.NgoProfileQueries =
    require("../ngo-profile-queries/ngo-profile-query.model")(sequelize);
  db.Cart = require("../carts/cart.model")(sequelize);
  db.CartItem = require("../cart-items/cart-item.model")(sequelize);
  db.Bucket = require("../buckets/bucket.model")(sequelize);
  db.BucketItem = require("../bucket-items/bucket-item.model")(sequelize);
  db.UserNotification = require("../user-notification/user-notification.model")(
    sequelize
  );
  db.KycInformation = require("../kyc-informations/kyc-information.model")(
    sequelize
  );
  db.DashboardStat = require("../dashboard-stats/dashboard-stat.model")(
    sequelize
  );

  // sync all models with database
  await sequelize.sync();

  db.sequelize = sequelize;
}
