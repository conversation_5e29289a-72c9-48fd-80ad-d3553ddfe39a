﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const axios = require("axios");
const orderDonationService = require("../order-donations/order-donation.service");
const Razorpay = require("razorpay");
const crypto = require("crypto");
const config = require("../../config.json");
const {
  calculateOrderAmounts,
  calculateTransactionAmounts,
} = require("../_helpers/donationConstants");

const KEY_ID = config.RAZORPAY_KEY_ID;
const KEY_SECRET = config.RAZORPAY_KEY_SECRET;
const auth = Buffer.from(`${KEY_ID}:${KEY_SECRET}`).toString("base64");

module.exports = {
  getAll,
  getById,
  getDonationsCalculation,
  create,
  donate,
  update,
  patch,
  delete: _delete,
};

db.Order.belongsTo(db.Ngo, {
  as: "ngoInfo",
  through: "ngos",
  foreignKey: "ngo_id",
  otherKey: "ngo_id",
});
db.Order.belongsTo(db.Product, {
  as: "productInfo",
  through: "products",
  foreignKey: "product_id",
  otherKey: "product_id",
});
db.Order.belongsTo(db.User, {
  as: "userInfo",
  through: "users",
  foreignKey: "user_id",
  otherKey: "user_id",
});

async function getAll(params) {
  const { user_id, ngo_id, product_id } = params;

  const where = {};
  if (user_id) where.user_id = user_id;
  if (ngo_id) where.ngo_id = ngo_id;
  if (product_id) where.product_id = product_id;

  const orders = await db.Order.findAll({
    where,
    order: [["id", "DESC"]],
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["id", "name"],
      },
      {
        model: db.Product,
        as: "productInfo",
        attributes: ["id", "name"],
      },
      {
        model: db.User,
        as: "userInfo",
        attributes: ["id", "fullname"],
      },
    ],
  });

  const orderIds = orders.map((order) => order.id);

  // Fetch order donations separately
  const orderDonations = await db.OrderDonation.findAll({
    where: { order_id: orderIds },
    attributes: ["id", "order_id", "campaign_id"],
    include: [
      {
        model: db.Campaign,
        as: "campaignInfo",
        attributes: ["id", "name"],
      },
    ],
  });

  // Group donations by order ID
  const donationsMap = {};
  orderDonations.forEach((donation) => {
    if (!donationsMap[donation.order_id]) {
      donationsMap[donation.order_id] = [];
    }
    donationsMap[donation.order_id].push(donation);
  });

  // Append donations to each order
  const result = orders.map((order) => ({
    ...order.toJSON(),
    orderDonations: donationsMap[order.id] || [],
  }));

  return result;
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function getDonationsCalculation(params) {
  const { baseAmount } = params;

  if (!baseAmount) {
    throw new Error("baseAmount is required");
  }

  const baseAmountNum = parseFloat(baseAmount);
  if (isNaN(baseAmountNum) || baseAmountNum <= 0) {
    throw new Error("baseAmount must be a valid positive number");
  }

  // Calculate order amounts
  const orderCalculations = calculateOrderAmounts(baseAmountNum);

  // Calculate transaction amounts
  const transactionCalculations = calculateTransactionAmounts(
    orderCalculations.totalAmount,
    orderCalculations.baseAmount,
    orderCalculations.drTip,
    orderCalculations.userConvenience
  );

  // Static tip amounts
  const tipAmounts = [20, 50, 100];

  return {
    orderLevel: {
      totalDrTip: orderCalculations.drTip,
      totalUserConvenience: orderCalculations.userConvenience,
      totalDonation: orderCalculations.totalAmount,
    },
    transactionLevel: {
      paymentGatewayCharges: transactionCalculations.paymentGatewayCharges,
      moniesAvailableForRouting:
        transactionCalculations.moniesAvailableForRouting,
      drTipAfterCharges: transactionCalculations.drTipAfterCharges,
      userConvenienceAfterCharges:
        transactionCalculations.userConvenienceAfterCharges,
      ngoPlatformConvenienceFee:
        transactionCalculations.ngoPlatformConvenienceFee,
      ngoNetDonation: transactionCalculations.ngoNetDonation,
    },
    tipAmounts: tipAmounts,
  };
}

async function create(params) {
  const { ngo_id, Campaigns, base_amount, ...orderParams } = params;

  // Calculate donation amounts if base_amount is provided
  let calculatedAmounts = {};
  if (base_amount) {
    calculatedAmounts = calculateOrderAmounts(parseFloat(base_amount));
  }

  const order = await db.Order.create({
    ...orderParams,
    base_amount: calculatedAmounts.baseAmount || base_amount,
    dr_tip: calculatedAmounts.drTip,
    user_convenience: calculatedAmounts.userConvenience,
    total_price: calculatedAmounts.totalAmount || orderParams.total_price,
    order_status: "Pending",
    order_prefix: "ORD_",
  });

  if (
    params.type === "ngo" ||
    params.type === "generic" ||
    params.type === "genericViaMoney"
  ) {
    for (const ngoId of params.ngo_id || []) {
      const ngoCategory = await db.NgoCategory.findOne({
        where: { ngo_id: ngoId },
      });

      if (!ngoCategory) continue;

      await db.OrderNgo.create({
        order_id: order.id,
        ngo_id: ngoId,
        cause_id: ngoCategory.category_id,
      });
    }
  }

  if (
    params.type === "campaign" ||
    params.type === "generic" ||
    params.type === "genericViaMoney"
  ) {
    for (const campaignId of params.Campaigns || []) {
      const campaign = await db.Campaign.findByPk(campaignId);
      if (!campaign) continue;

      await db.OrderCampaign.create({
        order_id: order.id,
        campaign_id: campaignId,
        cause_id: campaign.category_id,
      });
    }
  }

  // Calculate transaction amounts if we have the order calculations
  let transactionCalculations = {};
  if (calculatedAmounts.totalAmount && calculatedAmounts.baseAmount) {
    transactionCalculations = calculateTransactionAmounts(
      calculatedAmounts.totalAmount,
      calculatedAmounts.baseAmount,
      calculatedAmounts.drTip,
      calculatedAmounts.userConvenience
    );
  }

  const transactionPayload = {
    order_id: order.id,
    amount: calculatedAmounts.totalAmount || params?.total_price,
    user_id: params?.user_id,
    status: "Pending",
    donation_type: params?.type,
    order_prefix: order.order_prefix,
    order_number: order.order_id,
    ngo_id: order.ngo_id || null,
    campaign_id: order.campaign_id || null,
    bucket_id: order.bucket_id || null,
    payment_gateway_charges: transactionCalculations.paymentGatewayCharges,
    dr_tip_after_charges: transactionCalculations.drTipAfterCharges,
    user_convenience_after_charges:
      transactionCalculations.userConvenienceAfterCharges,
    ngo_platform_convenience_fee:
      transactionCalculations.ngoPlatformConvenienceFee,
    ngo_net_donation: transactionCalculations.ngoNetDonation,
    // impact_created: impact_created || null,
  };
  const transactionRecord = await db.Transaction.create(transactionPayload);
  const paymentURL = await createOrderForMobile({
    ...transactionRecord?.dataValues,
    order_prefix: order.order_prefix,
    order_number: order.order_id,
  });
  return {
    ...transactionRecord?.dataValues,
    order_prefix: order.order_prefix,
    order_number: order.order_id,
    paymentURL,
  };
}

async function createOrderForMobile(params) {
  const {
    amount,
    currency = "INR",
    order_id,
    order_prefix,
    order_number,
  } = params;

  try {
    const razorpay = new Razorpay({
      key_id: KEY_ID,
      key_secret: KEY_SECRET,
    });

    const options = {
      amount: Number(amount) * 100, // Razorpay expects amount in paise
      currency,
      // receipt: receipt || `rcptid_${Date.now()}`,
      payment_capture: 1, // Auto-capture
      // notes: notes || {},
    };

    const order = await razorpay.orders.create(options);
    // const callbackUrl = `https://dorightapi.interosys.in/api/payments/verifyPayment?orderId=${order_id}`;
    let callbackUrl = `http://localhost:4000/api/payments/verifyPayment?orderId=${order_id}`;
    if (config.environment === "UAT") {
      callbackUrl = `https://dorightapi.interosys.in/api/payments/verifyPayment?orderId=${order_id}`;
    }
    if (config.environment === "PROD") {
      callbackUrl = `https://doright.world/api/payments/verifyPayment?orderId=${order_id}`;
    }

    const params = new URLSearchParams({
      key_id: KEY_ID,
      amount: (Number(amount) * 100).toString(),
      currency: "INR",
      name: "doRight",
      order_id: order.id,
      redirect: "true",
      callback_url: callbackUrl,
    });

    const paymentUrl = `https://api.razorpay.com/v1/checkout/embedded?${params.toString()}`;

    return paymentUrl;
  } catch (error) {
    throw new Error(error?.message);
    return;
    // console.error("Create order error:", error);
    // return res.status(500).json({
    //   success: false,
    //   error: "Failed to create order",
    //   details: error.message,
    // });
  }
}

async function donate(params) {
  try {
    // Creating the order
    const orderData = await db.Order.create(params);

    // Creating the transaction
    const transaction = await db.Transaction.create({
      order_id: orderData.id,
      ngo_id: orderData.ngo_id,
      user_id: orderData.user_id,
      amount: orderData.total_price,
      donation_type: "donation",
    });

    // Capturing the payment from Razorpay
    // const captureResponse = await axios.post(
    //   `https://api.razorpay.com/v1/payments/${paymentId}/capture`,
    //   { amount: 2000, currency: "INR" }, // Ensure the amount matches the original payment
    //   {
    //     headers: {
    //       "content-type": "application/json",
    //       Authorization: `Basic ${auth}`,
    //     },
    //   }
    // );

    let captureResponse = await axios.get(
      `https://api.razorpay.com/v1/payments/${paymentId}`,
      {
        headers: {
          Authorization: `Basic ${auth}`,
        },
      }
    );

    const paymentstatus = captureResponse?.data?.status;

    if (paymentstatus === "authorized") {
      captureResponse = await axios.post(
        `https://api.razorpay.com/v1/payments/${paymentId}/capture`,
        { amount: 2000000, currency: "INR" },
        {
          headers: {
            "content-type": "application/json",
            Authorization: `Basic ${auth}`,
          },
        }
      );
    }

    const { id, order_id, status, amount, currency, method, description } =
      captureResponse?.data;

    // Updating the transaction with payment details

    await db.Transaction.update(
      {
        amount: amount,
        order_id: orderData.id,
        razorpay_payment_id: id,
        status: status,
        payment_mode: method,
        all_values: JSON.stringify(captureResponse?.data),
        description: description,
      },
      { where: { id: transaction.id } }
    );
    // await update(transaction.id, {
    //   amount: amount,
    //   order_id: orderData.id,
    //   razorpay_payment_id: id,
    //   status: "captured",
    //   payment_mode: method,
    //   all_values: JSON.stringify(captureResponse.data),
    //   description: description,
    // });

    // Transfer data for Razorpay transfer
    const transferData = {
      transfers: [
        {
          account: "acc_PpImPzYY1ctZC7",
          amount: 100,
          currency: "INR",
          notes: {
            branch: "ADYAR SHAYADRI CAMPUS",
            name: "Test Private Limited",
          },
          linked_account_notes: ["branch"],
          on_hold: 1,
        },
        {
          account: "acc_PpIgQv2w5MDAZe",
          amount: 100,
          currency: "INR",
          notes: {
            branch: "ADYAR SHAYADRI CAMPUS",
            name: "Test Business",
          },
          linked_account_notes: ["branch"],
          on_hold: 0,
        },
      ],
    };

    // Transfer funds using Razorpay API
    const transferResponse = await axios.post(
      `https://api.razorpay.com/v1/payments/${paymentId}/transfers`,
      transferData,
      {
        headers: {
          "content-type": "application/json",
          Authorization: `Basic ${auth}`,
        },
      }
    );

    await db.Transaction.update(
      {
        transferResponse: JSON.stringify(transferResponse?.data),
      },
      { where: { id: transaction.id } }
    );

    await db.Order.update(
      {
        payment_status: "Confirmed",
      },
      { where: { id: orderData.id } }
    );

    return {
      order: orderData,
      transaction: transaction,
      captureResponse: captureResponse?.data,
      transferResponse: transferResponse?.data,
    };
  } catch (error) {
    console.error(
      "Error occurred:",
      error.response ? error.response.data : error.message
    );

    // Handling specific errors based on status code or message
    if (error.response && error.response.status === 400) {
      // Handle bad request error (400)
      throw new Error("Bad request - Check the parameters.");
    } else if (error.response && error.response.status === 404) {
      // Handle not found error (404)
      throw new Error("Payment not found - Check the payment ID.");
    } else if (error.response && error.response.status === 500) {
      // Handle server error (500)
      throw new Error("Server error - Please try again later.");
    } else {
      // General error handler
      throw new Error("An unexpected error occurred.");
    }
  }
}

async function update(id, params) {
  const record = await getTransactionSingleRecord(id);
  const orderRecord = await getSingleRecord(record?.order_id);
  const transactionRecord = await db.Transaction.findOne({
    where: { order_id: id },
  });
  if (!transactionRecord)
    throw new Error("Transaction not found for the given order ID");

  // Copy params to transaction and save
  Object.assign(record, params);
  await record.save();

  const orderPayload = {
    paymentId: id,
    payment_status: params?.status,
  };
  Object.assign(orderRecord, orderPayload);
  await orderRecord.save();

  // If payment is captured or completed, perform transfer
  if (["captured", "completed"].includes(params?.status)) {
    const transferData = {
      transfers: [
        {
          account: "acc_PpImPzYY1ctZC7",
          amount: 100,
          currency: "INR",
          notes: {
            branch: "ADYAR SHAYADRI CAMPUS",
            name: "Test Private Limited",
          },
          linked_account_notes: ["branch"],
          on_hold: 1,
        },
        {
          account: "acc_PpIgQv2w5MDAZe",
          amount: 100,
          currency: "INR",
          notes: {
            branch: "ADYAR SHAYADRI CAMPUS",
            name: "Test Business",
          },
          linked_account_notes: ["branch"],
          on_hold: 0,
        },
      ],
    };

    try {
      const transferResponse = await axios.post(
        `https://api.razorpay.com/v1/payments/${params?.paymentId}/transfers`,
        transferData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Basic ${auth}`,
          },
        }
      );

      // Update transaction with transfer response
      await db.Transaction.update(
        {
          transferResponse: JSON.stringify(transferResponse?.data),
        },
        { where: { id: transactionRecord.id } }
      );

      // Update order status to Confirmed
      await db.Order.update(
        {
          payment_status: "Confirmed",
        },
        { where: { id: orderRecord.id } }
      );
    } catch (error) {
      console.error(
        "Transfer API error:",
        error?.response?.data || error.message
      );
      // Optionally throw or log error for further handling
    }
  }

  return record.get();
}

async function patch(id, params) {
  const record = await getSingleRecord(id);

  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.Order.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}

async function getTransactionSingleRecord(id) {
  const record = await db.Transaction.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
