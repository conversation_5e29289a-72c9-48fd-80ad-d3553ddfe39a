﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const { Op } = require("sequelize");
const { Sequelize } = require("sequelize");
const { format } = require("date-fns");

module.exports = {
  getAll,
  getById,
  create,
  update,
  patch,
  delete: _delete,
  getCampaignsByStatusParam,
  getCampaignsByNgoName,
  getCampaignsByLocationOrCategoryOrSkills,
  getCampaignStats,
  getFullCampaignStats,
  markAsFeatured,
  getCampaigns,
  getUpcomingCampaigns,
  getCampaignsByFilters,
  getByCategoryIds,
};
db.Campaign.belongsTo(db.Ngo, {
  as: "ngoInfo",
  through: "ngos",
  foreignKey: "ngo_id",
  otherKey: "ngo_id",
});
db.Campaign.belongsTo(db.Category, {
  as: "categoryInfo",
  through: "categories",
  foreignKey: "category_id",
  otherKey: "category_id",
});
db.KindDonation.belongsTo(db.Category, {
  as: "kindDonation",
  through: "campaign_kinddonations",
  foreignKey: "campaign_id",
  otherKey: "campaign_id",
});

async function getAll(params) {
  const where = {};
  const { ngoId, status, type } = params;
  if (ngoId) {
    where.ngo_id = ngoId;
  }
  if (status) {
    where.status = status;
  }

  if (type === "Event") {
    where.fund_raising_target = null;
  } else if (type === "Campaign") {
    where.fund_raising_target = { [Op.ne]: null };
  }

  const campaigns = await db.Campaign.findAll({
    order: [["id", "DESC"]],
    where: where,
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["id", "name"],
      },
      {
        model: db.Category,
        as: "categoryInfo",
        attributes: ["id", "name"],
      },
    ],
  });

  return campaigns;
}

async function getById(id, params) {
  const record = await getSingleRecord(id);

  if (params?.userId) {
    const isParticipated = await db.CampaignRsvps.count({
      where: {
        campaign_id: id,
        user_id: params.userId,
        rsvp_value: "yes",
      },
    });

    record.setDataValue("isParticipated", isParticipated ? "yes" : undefined);
  }

  // Track campaign view for CTR calculation
  try {
    const { format } = require("date-fns");
    const today = format(new Date(), "yyyy-MM-dd");

    // Check if record already exists for today
    const existingRecord = await db.DashboardStat.findOne({
      where: {
        type: "campaign",
        typeid: id,
        dateRecorded: today,
      },
    });

    // Only create if doesn't exist
    if (!existingRecord) {
      await db.DashboardStat.create({
        type: "campaign",
        typeid: id,
        dateRecorded: today,
      });
    }
  } catch (error) {
    // Log error but don't fail the main request
    console.error("Error tracking campaign view:", error);
  }

  return record;
}

async function getByCategoryIds(categoryIds) {
  if (!Array.isArray(categoryIds) || categoryIds.length === 0) {
    return [];
  }

  const campaigns = await db.Campaign.findAll({
    where: {
      category_id: {
        [Op.in]: categoryIds,
      },
    },
    order: [["id", "DESC"]],
  });

  return campaigns;
}

async function create(params) {
  if ("name" in params && params.name) {
    await utils.validateTextForAbuse(params.name);
  }

  if ("description" in params && params.description) {
    await utils.validateTextForAbuse(params.description);
  }

  const record = await db.Campaign.create(params);

  // Track campaign creation in dashboard stats
  try {
    const { format } = require("date-fns");
    const today = format(new Date(), "yyyy-MM-dd");

    await db.DashboardStat.create({
      type: "campaign_created",
      typeid: record.id,
      dateRecorded: today,
      createdBy: params.createdBy || null,
    });
  } catch (error) {
    // Log error but don't fail the main request
    console.error("Error tracking campaign creation:", error);
  }

  return record;
}

//helper fucntion

async function getCampaignsByFilters(filters, params) {
  const {
    latitude,
    longitude,
    radius = 10,
    ngos,
    causes,
    certificates,
    ngoType,
    search,
    page = 1,
    limit = 10,
    type = "Campaign",
  } = filters;

  const offset = (page - 1) * limit;

  // Step 1: Get base NGO list (as you have it)
  let ngoIds = [];
  if (Array.isArray(ngos) && ngos.length > 0) {
    ngoIds = ngos.map((ngo) => ngo.id);
  } else {
    const verifiedNgos = await db.Ngo.findAll({
      where: { ngo_status: "Verified" },
      attributes: ["id", "grade", "registration_details"],
    });
    ngoIds = verifiedNgos.map((ngo) => ngo.id);
  }

  // Step 2: Apply NGO Type Filter (same as before)
  if (Array.isArray(ngoType) && ngoType.length > 0) {
    const gradeValues = ngoType
      .map((type) => {
        if (type.includes("Gold")) return "Gold";
        if (type.includes("Silver")) return "Silver";
        if (type.includes("Bronze")) return "Bronze";
        return null;
      })
      .filter(Boolean);

    const filteredByType = await db.Ngo.findAll({
      where: {
        id: { [Op.in]: ngoIds },
        grade: { [Op.in]: gradeValues },
      },
      attributes: ["id"],
    });

    ngoIds = filteredByType.map((ngo) => ngo.id);
  }

  // Step 3: Apply Certificate Filter (same as before)
  if (Array.isArray(certificates) && certificates.length > 0) {
    const certConditions = certificates.map((cert) =>
      Sequelize.literal(
        `JSON_UNQUOTE(JSON_EXTRACT(CAST(registration_details AS JSON), '$.${cert}')) IS NOT NULL`
      )
    );

    const certifiedNgos = await db.Ngo.findAll({
      where: {
        id: { [Op.in]: ngoIds },
        [Op.and]: certConditions,
      },
      attributes: ["id"],
    });

    ngoIds = certifiedNgos.map((ngo) => ngo.id);
  }

  // Step 4: Fetch campaigns/events by location FIRST (location is priority)
  // Get all active campaigns/events
  const baseWhereCondition = {
    status: "Active",
  };
  if (type === "Event") {
    baseWhereCondition.fund_raising_target = null; // Events have no fundraising target
  } else if (type === "Campaign") {
    baseWhereCondition.fund_raising_target = { [Op.ne]: null }; // Campaigns must have a fundraising target
  }

  // Fetch campaigns without NGO filtering first, so we can filter by location
  let campaigns = await db.Campaign.findAll({
    where: baseWhereCondition,
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["id", "name", "grade", "registration_details"],
      },
      { model: db.Category, as: "categoryInfo", attributes: ["id", "name"] },
    ],
  });

  // Filter campaigns by location radius
  if (latitude && longitude) {
    campaigns = campaigns.filter((campaign) => {
      const campLat = parseFloat(campaign.latitude);
      const campLng = parseFloat(campaign.longitude);
      if (isNaN(campLat) || isNaN(campLng)) return false;

      const distance = utils.haversineDistance(
        latitude,
        longitude,
        campLat,
        campLng
      );
      return distance <= radius;
    });
  }

  // Step 5: Filter campaigns based on filtered NGO IDs
  campaigns = campaigns.filter((campaign) => ngoIds.includes(campaign.ngo_id));

  // Step 6: Apply remaining filters on campaigns

  // Causes filter
  if (Array.isArray(causes) && causes.length > 0) {
    const causeIds = causes.map((cause) => cause.id);
    campaigns = campaigns.filter((campaign) =>
      causeIds.includes(campaign.category_id)
    );
  }

  // Search filter
  if (search) {
    campaigns = campaigns.filter((campaign) =>
      campaign.name.toLowerCase().includes(search.toLowerCase())
    );
  }

  // Step 7: Paginate final results
  const totalCount = campaigns.length;
  const totalPages = Math.ceil(totalCount / limit);
  const paginatedCampaigns = campaigns.slice(offset, offset + limit);

  return {
    data: paginatedCampaigns,
    pagination: {
      currentPage: page,
      totalPages,
      totalCount,
    },
  };
}

function degreesToRadians(degrees) {
  return (degrees * Math.PI) / 180;
}

// async function update(id, params) {
//   const record = await getSingleRecord(id);

//   // copy params to user and save
//   Object.assign(record, params);
//   await record.save();

//   return record.get();
// }
async function update(id, params) {
  if ("name" in params && params.name) {
    await utils.validateTextForAbuse(params.name);
  }

  if ("description" in params && params.description) {
    await utils.validateTextForAbuse(params.description);
  }
  const record = await getSingleRecord(id);

  const { inKindList, createdBy } = params;
  const parsedInKindList =
    typeof inKindList === "string" ? JSON.parse(inKindList) : inKindList;

  // Remove the `inKindList` from the params to avoid direct assignment to the Campaign table
  delete params.inKindList;

  // Update campaign fields
  Object.assign(record, params);
  await record.save();

  if (Array.isArray(parsedInKindList)) {
    // Delete existing kind donations for the campaign
    await db.KindDonation.destroy({
      where: {
        campaign_id: id,
      },
    });

    // Add new kind donations if provided
    if (parsedInKindList.length > 0) {
      const kindDonations = parsedInKindList.map((item) => ({
        item_name: item.item,
        quantity: parseInt(item.quantity, 10),
        unit_of_measure: item.unit,
        campaign_id: id,
        createdBy: createdBy,
      }));

      try {
        await db.KindDonation.bulkCreate(kindDonations, {
          validate: true,
        });
      } catch (error) {
        console.error("Error during bulk insert of kind donations:", error);
        throw error;
      }
    }
  }

  // Track campaign update in dashboard stats
  try {
    const { format } = require("date-fns");
    const today = format(new Date(), "yyyy-MM-dd");

    // Check if record already exists for today
    const existingRecord = await db.DashboardStat.findOne({
      where: {
        type: "campaign_updated",
        typeid: id,
        dateRecorded: today,
      },
    });

    // Only create if doesn't exist
    if (!existingRecord) {
      await db.DashboardStat.create({
        type: "campaign_updated",
        typeid: id,
        dateRecorded: today,
        updatedBy: params.updatedBy || null,
      });
    }
  } catch (error) {
    // Log error but don't fail the main request
    console.error("Error tracking campaign update:", error);
  }

  return record.get();
}
async function patch(id, params) {
  if ("name" in params && params.name) {
    await utils.validateTextForAbuse(params.name);
  }

  if ("description" in params && params.description) {
    await utils.validateTextForAbuse(params.description);
  }

  const record = await getSingleRecord(id);
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.Campaign.findByPk(id, {
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["id", "name"],
      },
    ],
  });
  if (!record) throw "Record not found";

  record.dataValues.fund_raised = 10000;
  return record;
}

async function getCampaignsByStatusParam(
  statusParam,
  ngoId,
  page = 1,
  limit = 50,
  portalUserId,
  searchTerm,
  params
) {
  const { type } = params;
  const offset = (page - 1) * limit;

  let where = {};
  if (ngoId) {
    where.ngo_id = ngoId;
  }

  let filteredNgoIds = [];

  if (portalUserId) {
    const ngoRecords = await db.Ngo.findAll({
      where: { assignee_id: portalUserId },
      attributes: ["id"],
    });

    filteredNgoIds = ngoRecords.map(({ id }) => id);

    if (filteredNgoIds.length > 0) {
      where.ngo_id = filteredNgoIds;
    } else {
      return {
        result: [],
        totalCount: 0,
        totalPages: 0,
        currentPage: page,
        statusCountsMap: {},
      };
    }
  }

  // Add searchTerm filter if provided
  if (searchTerm) {
    where[Op.or] = [{ name: { [Op.like]: `%${searchTerm}%` } }];
  }

  if (statusParam === "campaigns") {
    where.fund_raising_target = { [Op.ne]: null };
  } else {
    where.fund_raising_target = null; // Include only events
  }

  // Get campaigns based on filters with pagination
  const { count, rows } = await db.Campaign.findAndCountAll({
    where: where,
    limit: parseInt(limit),
    offset: parseInt(offset),
    order: [["id", "DESC"]],
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["id", "name"],
      },
      {
        model: db.Category,
        as: "categoryInfo",
        attributes: ["id", "name"],
      },
    ],
  });

  if (!rows || rows.length === 0) {
    return { result: [], totalCount: 0, totalPages: 0, currentPage: page };
  }

  const updatedCampaignsRecord = await Promise.all(
    rows.map(async (campaign) => {
      campaign.fund_raised = 10000;
      return campaign;
    })
  );

  let statusWhereClause = {};
  if (portalUserId) {
    statusWhereClause = { ngo_id: filteredNgoIds };
  } else if (ngoId) {
    statusWhereClause = { ngo_id: ngoId };
  }

  // Get status counts for campaigns
  const statusCounts = await db.Campaign.findAll({
    attributes: [
      "status",
      [Sequelize.fn("COUNT", Sequelize.col("status")), "count"],
    ],
    where: statusWhereClause,
    group: ["status"],
  });

  const statusCountsMap = statusCounts.reduce((acc, item) => {
    if (item.status !== null) {
      acc[item.status] = parseInt(item.dataValues.count, 10);
    }
    return acc;
  }, {});

  if (portalUserId) {
    statusCountsMap["All"] = await db.Campaign.count({
      where: { ngo_id: filteredNgoIds },
    });
  } else if (ngoId) {
    statusCountsMap["All"] = await db.Campaign.count({
      where: { ngo_id: ngoId },
    });
  } else {
    statusCountsMap["All"] = await db.Campaign.count();
  }

  const totalCount = count;
  const totalPages = Math.ceil(totalCount / limit);

  return {
    result: updatedCampaignsRecord,
    totalCount,
    totalPages,
    currentPage: page,
    statusCountsMap,
  };
}

async function getCampaignsByNgoName(ngoName) {
  const result = await db.Campaign.findAll({
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        where: {
          name: {
            [Op.like]: `%${ngoName}%`,
          },
        },
        attributes: ["id", "name"],
      },
    ],
    order: [["id", "DESC"]],
  });

  if (!result.length) return "No campaigns found for the given NGO name";
  return result;
}

async function getCampaignsByLocationOrCategoryOrSkills({
  latitude,
  longitude,
  radius = 10, // default radius in km
  categoryId,
  skills,
}) {
  const where = {};

  // Include category filter if provided
  if (categoryId) {
    where.category_id = categoryId;
  }

  // Skills-based filter (if campaigns or NGOs have a `skills` field)
  if (skills) {
    where.skills = {
      [Op.like]: `%${skills}%`,
    };
  }

  // Get all campaigns and filter manually for location-based distance
  const campaigns = await db.Campaign.findAll({
    where,
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["id", "name"],
      },
      {
        model: db.Category,
        as: "categoryInfo",
        attributes: ["id", "name"],
      },
    ],
    order: [["id", "DESC"]],
  });

  // Filter by location manually using Haversine formula
  if (latitude && longitude) {
    const EARTH_RADIUS = 6371; // Earth radius in km

    return campaigns.filter((campaign) => {
      const campaignLat = parseFloat(campaign.latitude);
      const campaignLng = parseFloat(campaign.longitude);

      if (isNaN(campaignLat) || isNaN(campaignLng)) return false;

      // Haversine formula
      const dLat = degreesToRadians(campaignLat - latitude);
      const dLng = degreesToRadians(campaignLng - longitude);
      const lat1 = degreesToRadians(latitude);
      const lat2 = degreesToRadians(campaignLat);

      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.sin(dLng / 2) *
          Math.sin(dLng / 2) *
          Math.cos(lat1) *
          Math.cos(lat2);

      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      const distance = EARTH_RADIUS * c; // Distance in km

      return distance <= radius; // Filter campaigns within radius
    });
  }

  return campaigns.length
    ? campaigns
    : "No campaigns found matching the criteria";
}

// Helper function to convert degrees to radians
function degreesToRadians(degrees) {
  return degrees * (Math.PI / 180);
}
async function getCampaignStats(ngoId) {
  const totalCampaigns = await db.Campaign.count({
    where: { ngo_id: ngoId },
  });
  const completed = await db.Campaign.count({
    where: { status: "Completed", ngo_id: ngoId },
  });
  const live = await db.Campaign.count({
    where: { status: "Live", ngo_id: ngoId },
  });
  const approved = await db.Campaign.count({
    where: { status: "Approved", ngo_id: ngoId },
  });
  const inReview = await db.Campaign.count({
    where: { status: "In Review", ngo_id: ngoId },
  });
  const draft = await db.Campaign.count({
    where: { status: "Draft", ngo_id: ngoId },
  });

  return {
    campaignStats: {
      totalCampaigns,
      completed,
      live,
      approved,
      inReview,
      draft,
    },
  };
}
async function getFullCampaignStats() {
  const totalCampaigns = await db.Campaign.count();
  const completed = await db.Campaign.count({
    where: { status: "Completed" },
  });
  const live = await db.Campaign.count({
    where: { status: "Live" },
  });
  const approved = await db.Campaign.count({
    where: { status: "Approved" },
  });
  const inReview = await db.Campaign.count({
    where: { status: "In Review" },
  });
  const draft = await db.Campaign.count({
    where: { status: "Draft" },
  });

  return {
    alertMessage: {
      inReviewCampaginCount: totalCampaigns,
    },

    campaginStats: {
      totalCampaigns: totalCampaigns,
      live: live,
      inReview: inReview,
      approved: approved,
      completed: completed,
    },
  };
}
async function markAsFeatured(campaignId, featureMode) {
  if (!campaignId) {
    throw new Error("Campaign ID is required.");
  }

  const isFeatured = featureMode === "add";
  try {
    await update(campaignId, { isfeatured: isFeatured ? "yes" : "no" });
  } catch (error) {
    throw new Error(`Failed to mark campaign as featured: ${error.message}`);
  }
}

// Service Function
async function getCampaignsByLocationOrCategoryOrSkills(filters) {
  const { latitude, longitude, radius = 10, categoryId, skills } = filters;
  const where = {};

  if (categoryId) {
    where.category_id = categoryId;
  }

  if (skills) {
    where.skills = {
      [Op.like]: `%${skills}%`,
    };
  }

  const campaigns = await db.Campaign.findAll({
    where,
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["id", "name"],
      },
      {
        model: db.Category,
        as: "categoryInfo",
        attributes: ["id", "name"],
      },
    ],
    order: [["id", "DESC"]],
  });

  if (latitude && longitude) {
    const EARTH_RADIUS = 6371;

    return campaigns.filter((campaign) => {
      const campaignLat = parseFloat(campaign.latitude);
      const campaignLng = parseFloat(campaign.longitude);

      if (isNaN(campaignLat) || isNaN(campaignLng)) return false;

      // Haversine formula
      const dLat = degreesToRadians(campaignLat - latitude);
      const dLng = degreesToRadians(campaignLng - longitude);
      const lat1 = degreesToRadians(latitude);
      const lat2 = degreesToRadians(campaignLat);

      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.sin(dLng / 2) *
          Math.sin(dLng / 2) *
          Math.cos(lat1) *
          Math.cos(lat2);

      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      const distance = EARTH_RADIUS * c; // Distance in km

      return distance <= radius;
    });
  }

  return campaigns.length
    ? campaigns
    : "No campaigns found matching the criteria";
}

async function getCampaigns(params) {
  const { search, status, ngo_id, portalUserId } = params;

  let filteredNgoIds = [];
  if (portalUserId) {
    const ngoRecords = await db.Ngo.findAll({
      where: { assignee_id: portalUserId },
      attributes: ["id"],
    });

    filteredNgoIds = ngoRecords.map(({ id }) => id);
  }

  const whereClause = {};

  if (search) {
    whereClause.name = {
      [Sequelize.Op.like]: `%${search}%`,
    };
  }

  if (status) {
    whereClause.status = status;
  }
  if (portalUserId) {
    whereClause.ngo_id = { [Sequelize.Op.in]: filteredNgoIds };
  } else if (ngo_id) {
    whereClause.ngo_id = ngo_id;
  }

  return db.Campaign.findAll({ where: whereClause });
}

async function getUpcomingCampaigns(params) {
  const { type } = params;

  const today = format(new Date(), "yyyy-MM-dd"); // Get today's date in YYYY-MM-DD format
  let order = [];
  let where = {};

  if (type === "Event") {
    where.fund_raising_target = null;
    where.event_date = { [Op.gt]: today };
    order = [["event_date", "ASC"]];
  } else if (type === "Campaign") {
    where.fund_raising_target = { [Op.ne]: null };
  }

  const queryOptions = {
    where,
    order,
  };

  return await db.Campaign.findAll(queryOptions);
}
